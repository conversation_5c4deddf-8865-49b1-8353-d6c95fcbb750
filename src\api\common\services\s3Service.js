const { S3Client, PutObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { fromIni } = require('@aws-sdk/credential-provider-ini');
const dotenv = require('dotenv');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

dotenv.config();

// S3 Configuration
const REGION = process.env.AWS_REGION || process.env.REGION || 'ca-central-1';
const BUCKET_NAME = process.env.S3_BUCKET_NAME || 'gigmosaic-reviews-images';
const ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID || process.env.ACCESSKEY_ID;
const SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY || process.env.SECRETACCESSKEY;

// Initialize S3 Client
const s3Client = new S3Client({
    region: REGION,
    credentials: {
        accessKeyId: ACCESS_KEY_ID,
        secretAccessKey: SECRET_ACCESS_KEY,
    },
});

/**
 * Upload image to S3
 * @param {Buffer} fileBuffer - File buffer
 * @param {string} fileName - Original file name
 * @param {string} mimeType - File MIME type
 * @param {string} folder - S3 folder (default: 'reviews')
 * @returns {Promise<Object>} Upload result with imageName and fullUrl
 */
const uploadImage = async (fileBuffer, fileName, mimeType, folder = 'reviews') => {
    try {
        // Generate unique file name
        const fileExtension = fileName.split('.').pop();
        const uniqueFileName = `${folder}/${Date.now()}-${uuidv4()}.${fileExtension}`;

        const uploadParams = {
            Bucket: BUCKET_NAME,
            Key: uniqueFileName,
            Body: fileBuffer,
            ContentType: mimeType,
            ACL: 'public-read', // Make images publicly accessible
        };

        const command = new PutObjectCommand(uploadParams);
        await s3Client.send(command);

        // Generate public URL
        const fullUrl = `https://${BUCKET_NAME}.s3.${REGION}.amazonaws.com/${uniqueFileName}`;

        logger.info(`Image uploaded successfully to S3: ${uniqueFileName}`);
        
        return {
            imageName: uniqueFileName,
            fullUrl: fullUrl,
            bucket: BUCKET_NAME,
            region: REGION
        };
    } catch (error) {
        logger.error(`Error uploading image to S3: ${error.message}`);
        throw new Error(`Failed to upload image: ${error.message}`);
    }
};

/**
 * Upload multiple images to S3
 * @param {Array} files - Array of file objects with buffer, originalname, and mimetype
 * @param {string} folder - S3 folder (default: 'reviews')
 * @returns {Promise<Array>} Array of upload results
 */
const uploadMultipleImages = async (files, folder = 'reviews') => {
    try {
        const uploadPromises = files.map(file => 
            uploadImage(file.buffer, file.originalname, file.mimetype, folder)
        );

        const results = await Promise.all(uploadPromises);
        logger.info(`Successfully uploaded ${results.length} images to S3`);
        
        return results;
    } catch (error) {
        logger.error(`Error uploading multiple images to S3: ${error.message}`);
        throw new Error(`Failed to upload images: ${error.message}`);
    }
};

/**
 * Generate full URL from S3 image name
 * @param {string} imageName - S3 image name/key
 * @returns {string} Full S3 URL
 */
const generateImageUrl = (imageName) => {
    if (!imageName) return null;
    
    // If it's already a full URL, return as is
    if (imageName.startsWith('http')) {
        return imageName;
    }
    
    return `https://${BUCKET_NAME}.s3.${REGION}.amazonaws.com/${imageName}`;
};

/**
 * Generate multiple image URLs from image names
 * @param {Array} imageNames - Array of S3 image names/keys
 * @returns {Array} Array of full S3 URLs
 */
const generateImageUrls = (imageNames) => {
    if (!imageNames || !Array.isArray(imageNames)) {
        return [];
    }
    
    return imageNames.map(imageName => ({
        imageName,
        fullUrl: generateImageUrl(imageName)
    }));
};

/**
 * Generate presigned URL for secure image access (if needed for private images)
 * @param {string} imageName - S3 image name/key
 * @param {number} expiresIn - URL expiration time in seconds (default: 3600)
 * @returns {Promise<string>} Presigned URL
 */
const generatePresignedUrl = async (imageName, expiresIn = 3600) => {
    try {
        const command = new GetObjectCommand({
            Bucket: BUCKET_NAME,
            Key: imageName,
        });

        const presignedUrl = await getSignedUrl(s3Client, command, { expiresIn });
        return presignedUrl;
    } catch (error) {
        logger.error(`Error generating presigned URL: ${error.message}`);
        throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
};

/**
 * Validate image file
 * @param {Object} file - File object
 * @returns {boolean} True if valid
 */
const validateImageFile = (file) => {
    const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    const maxFileSize = 5 * 1024 * 1024; // 5MB

    if (!allowedMimeTypes.includes(file.mimetype)) {
        throw new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.');
    }

    if (file.size > maxFileSize) {
        throw new Error('File size too large. Maximum size is 5MB.');
    }

    return true;
};

/**
 * Process review images for database storage
 * @param {Array} imageNames - Array of S3 image names
 * @returns {Array} Array of image objects with URLs
 */
const processReviewImages = (imageNames) => {
    if (!imageNames || !Array.isArray(imageNames)) {
        return [];
    }

    return imageNames.map(imageName => ({
        imageName,
        fullUrl: generateImageUrl(imageName),
        uploadedAt: new Date()
    }));
};

module.exports = {
    uploadImage,
    uploadMultipleImages,
    generateImageUrl,
    generateImageUrls,
    generatePresignedUrl,
    validateImageFile,
    processReviewImages,
    s3Client,
    BUCKET_NAME,
    REGION
};
