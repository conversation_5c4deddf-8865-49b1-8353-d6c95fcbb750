const multer = require('multer');
const { uploadMultipleImages, validateImageFile } = require('../../common/services/s3Service');
const logger = require('../../common/utils/logger');
const errorUtil = require('../../common/utils/error');

// Configure multer for memory storage
const storage = multer.memoryStorage();

// File filter for images only
const fileFilter = (req, file, cb) => {
    try {
        validateImageFile(file);
        cb(null, true);
    } catch (error) {
        cb(error, false);
    }
};

// Multer configuration
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB per file
        files: 5 // Maximum 5 files
    }
});

/**
 * Upload review images to S3
 * Middleware: upload.array('images', 5)
 */
const uploadReviewImages = async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'images', message: 'No images provided for upload.' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );
            return res.status(400).json(errorResponse);
        }

        // Validate file count
        if (req.files.length > 5) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'images', message: 'Maximum 5 images allowed per upload.' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );
            return res.status(400).json(errorResponse);
        }

        // Upload images to S3
        const uploadResults = await uploadMultipleImages(req.files, 'reviews');

        logger.info(`Successfully uploaded ${uploadResults.length} review images`);

        return res.status(200).json({
            success: true,
            message: `Successfully uploaded ${uploadResults.length} images`,
            images: uploadResults.map(result => ({
                imageName: result.imageName,
                fullUrl: result.fullUrl
            })),
            imageNames: uploadResults.map(result => result.imageName) // For easy database storage
        });

    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        let statusCode = 500;
        let errorType = errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR;

        // Handle specific error types
        if (error.message.includes('Invalid file type') || 
            error.message.includes('File size too large')) {
            statusCode = 400;
            errorType = errorUtil.ERROR_TYPES.VALIDATION_ERROR;
        }

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'images', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error uploading review images: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Upload single review image to S3
 * Middleware: upload.single('image')
 */
const uploadSingleReviewImage = async (req, res) => {
    try {
        if (!req.file) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'image', message: 'No image provided for upload.' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );
            return res.status(400).json(errorResponse);
        }

        // Upload single image to S3
        const uploadResult = await uploadMultipleImages([req.file], 'reviews');
        const result = uploadResult[0];

        logger.info(`Successfully uploaded single review image: ${result.imageName}`);

        return res.status(200).json({
            success: true,
            message: 'Successfully uploaded image',
            image: {
                imageName: result.imageName,
                fullUrl: result.fullUrl
            }
        });

    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        let statusCode = 500;
        let errorType = errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR;

        // Handle specific error types
        if (error.message.includes('Invalid file type') || 
            error.message.includes('File size too large')) {
            statusCode = 400;
            errorType = errorUtil.ERROR_TYPES.VALIDATION_ERROR;
        }

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'image', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error uploading single review image: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Get upload configuration and limits
 */
const getUploadConfig = (req, res) => {
    return res.status(200).json({
        success: true,
        config: {
            maxFiles: 5,
            maxFileSize: '5MB',
            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
            allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        }
    });
};

module.exports = {
    upload,
    uploadReviewImages,
    uploadSingleReviewImage,
    getUploadConfig
};
