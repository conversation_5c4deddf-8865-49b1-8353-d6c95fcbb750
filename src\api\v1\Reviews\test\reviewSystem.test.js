const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../../../app'); // Adjust path as needed
const Review = require('../model/reviewModel');

describe('Review System with Frontend Data Structure', () => {
    let authToken;
    let testReviewData;

    beforeAll(async () => {
        // Connect to test database
        if (mongoose.connection.readyState === 0) {
            await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/gigmosaic_test');
        }
    });

    beforeEach(async () => {
        // Clear reviews collection
        await Review.deleteMany({});
        
        // Mock auth token (adjust based on your auth system)
        authToken = 'Bearer test-token';
        
        // Test data matching frontend structure
        testReviewData = {
            providerId: 'PROV_123456',
            serviceId: 'SRV_789012',
            bookingId: 'BKG_345678',
            
            // Category ratings (1-4 scale)
            qualityRating: 4,
            timelinessRating: 3,
            communicationRating: 4,
            valueRating: 3,
            
            // Review content
            title: 'Excellent service quality',
            comment: 'The service was outstanding with great attention to detail. Highly recommended for anyone looking for quality work.',
            date: '2024-01-15T10:30:00.000Z',
            
            // Image names from S3 upload
            imageNames: [
                'review-image-1-20240115.jpg',
                'review-image-2-20240115.jpg'
            ]
        };
    });

    afterAll(async () => {
        await mongoose.connection.close();
    });

    describe('POST /api/v1/reviews', () => {
        it('should create a review with category ratings and image names', async () => {
            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(testReviewData)
                .expect(201);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Review created successfully');
            expect(response.body.review).toBeDefined();

            const review = response.body.review;
            
            // Check category ratings
            expect(review.qualityRating).toBe(4);
            expect(review.timelinessRating).toBe(3);
            expect(review.communicationRating).toBe(4);
            expect(review.valueRating).toBe(3);
            
            // Check calculated overall rating
            expect(review.overallRating).toBe(3.5); // (4+3+4+3)/4 = 3.5
            
            // Check content
            expect(review.title).toBe('Excellent service quality');
            expect(review.comment).toContain('outstanding');
            
            // Check image names
            expect(review.imageNames).toHaveLength(2);
            expect(review.imageNames).toContain('review-image-1-20240115.jpg');
            expect(review.imageNames).toContain('review-image-2-20240115.jpg');
            
            // Check metadata
            expect(review.providerId).toBe('PROV_123456');
            expect(review.serviceId).toBe('SRV_789012');
            expect(review.bookingId).toBe('BKG_345678');
            expect(review.status).toBe('approved');
        });

        it('should validate required category ratings', async () => {
            const invalidData = { ...testReviewData };
            delete invalidData.qualityRating;

            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(invalidData)
                .expect(422);

            expect(response.body.success).toBe(false);
            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    field: 'qualityRating',
                    message: 'qualityRating is required.'
                })
            );
        });

        it('should validate rating range (1-4)', async () => {
            const invalidData = { ...testReviewData, qualityRating: 5 };

            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(invalidData)
                .expect(422);

            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    field: 'qualityRating',
                    message: 'qualityRating must be an integer between 1 and 4.'
                })
            );
        });

        it('should validate image names array', async () => {
            const invalidData = { 
                ...testReviewData, 
                imageNames: ['', 'valid-name.jpg'] // Empty string should fail
            };

            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(invalidData)
                .expect(422);

            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    message: expect.stringContaining('must be a non-empty string')
                })
            );
        });

        it('should limit maximum images to 5', async () => {
            const invalidData = { 
                ...testReviewData, 
                imageNames: [
                    'image1.jpg', 'image2.jpg', 'image3.jpg', 
                    'image4.jpg', 'image5.jpg', 'image6.jpg'
                ]
            };

            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(invalidData)
                .expect(422);

            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    message: 'Maximum 5 images allowed per review.'
                })
            );
        });

        it('should require title and comment', async () => {
            const invalidData = { ...testReviewData };
            delete invalidData.title;
            delete invalidData.comment;

            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(invalidData)
                .expect(422);

            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    field: 'title',
                    message: 'title is required.'
                })
            );
            
            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    field: 'comment',
                    message: 'comment is required.'
                })
            );
        });

        it('should handle review without images', async () => {
            const dataWithoutImages = { ...testReviewData };
            delete dataWithoutImages.imageNames;

            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(dataWithoutImages)
                .expect(201);

            expect(response.body.review.imageNames).toEqual([]);
        });

        it('should prevent duplicate reviews for same booking', async () => {
            // Create first review
            await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(testReviewData)
                .expect(201);

            // Try to create duplicate review
            const response = await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(testReviewData)
                .expect(400);

            expect(response.body.errors).toContainEqual(
                expect.objectContaining({
                    message: 'You have already reviewed this booking.'
                })
            );
        });
    });

    describe('GET /api/v1/reviews', () => {
        beforeEach(async () => {
            // Create test review
            await request(app)
                .post('/api/v1/reviews')
                .set('Authorization', authToken)
                .send(testReviewData)
                .expect(201);
        });

        it('should retrieve reviews with category ratings', async () => {
            const response = await request(app)
                .get('/api/v1/reviews')
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.reviews).toHaveLength(1);
            
            const review = response.body.reviews[0];
            expect(review.qualityRating).toBe(4);
            expect(review.timelinessRating).toBe(3);
            expect(review.communicationRating).toBe(4);
            expect(review.valueRating).toBe(3);
            expect(review.overallRating).toBe(3.5);
        });

        it('should filter reviews by serviceId', async () => {
            const response = await request(app)
                .get('/api/v1/reviews?serviceId=SRV_789012')
                .expect(200);

            expect(response.body.reviews).toHaveLength(1);
            expect(response.body.reviews[0].serviceId).toBe('SRV_789012');
        });

        it('should filter reviews by providerId', async () => {
            const response = await request(app)
                .get('/api/v1/reviews?providerId=PROV_123456')
                .expect(200);

            expect(response.body.reviews).toHaveLength(1);
            expect(response.body.reviews[0].providerId).toBe('PROV_123456');
        });
    });
});
